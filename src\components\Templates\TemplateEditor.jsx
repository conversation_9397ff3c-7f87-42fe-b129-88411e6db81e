import React, { useState, useEffect } from "react";
import Header from "../Layout/Header";
import { Save, X, Plus, Trash2, FileText, GripVertical } from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import TemplateTabList from "./Component/TemplateTabList";

const TemplateEditor = ({ template, pages, onSave, onCancel }) => {
  const api = useHttp();
  const [formData, setFormData] = useState({
    name: "",
    description: "",
    pages: [],
  });
  const [availablePages, setAvailablePages] = useState([]);
  const [saving, setSaving] = useState(false);

  useEffect(() => {
    if (template) {
      setFormData({
        name: template.name || "",
        description: template.description || "",
        pages: template.pages || [],
      });
    }

    setAvailablePages(pages || []);
  }, [template, pages]);

  const handleSubmit = async (e) => {
    e.preventDefault();
    setSaving(true);

    const apiConfig = template
      ? apiGenerator(CONSTANTS.API.templates.update, { id: template.id })
      : CONSTANTS.API.templates.create;

    api.sendRequest(
      apiConfig,
      (res) => {
        console.log("Template saved successfully:", res);
        setSaving(false);
        onSave();
      },
      formData,
      template
        ? "Template updated successfully!"
        : "Template created successfully!",
      (error) => {
        console.error("Error saving template:", error);
        setSaving(false);
      }
    );
  };

  const addPageToTemplate = (pageId) => {
    if (!formData.pages.includes(pageId)) {
      setFormData({
        ...formData,
        pages: [...formData.pages, pageId],
      });
    }
  };

  const removePageFromTemplate = (pageId) => {
    setFormData({
      ...formData,
      pages: formData.pages.filter((id) => id !== pageId),
    });
  };

  const movePageInTemplate = (fromIndex, toIndex) => {
    const updatedPages = [...formData.pages];
    const [movedPage] = updatedPages.splice(fromIndex, 1);
    updatedPages.splice(toIndex, 0, movedPage);

    setFormData({
      ...formData,
      pages: updatedPages,
    });
  };

  const getPageById = (pageId) => {
    return availablePages.find((page) => page.id === pageId);
  };

  const unusedPages = availablePages.filter(
    (page) => !formData.pages.includes(page.id)
  );

  return (
    <>
      <div className="  tw-mx-auto">
        <TemplateTabList formData={formData} setFormData={setFormData} />
      </div>
      {/* <form onSubmit={handleSubmit}>
          <div className="tw-grid tw-grid-cols-1 tw-lg:tw-grid-cols-2 tw-gap-8"> */}
      {/* Left Panel - Template Details */}
      {/* <div className="tw-space-y-6"> */}
      {/* <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-6">
                <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-4">
                  Template Details
                </h3>

                <div className="tw-space-y-4">
                  <div>
                    <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                      Template Name
                    </label>
                    <input
                      type="text"
                      value={formData.name}
                      onChange={(e) =>
                        setFormData({ ...formData, name: e.target.value })
                      }
                      className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent"
                      placeholder="e.g., Business Website, Portfolio, Blog"
                      required
                    />
                  </div>

                  <div>
                    <label className="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">
                      Description
                    </label>
                    <textarea
                      value={formData.description}
                      onChange={(e) =>
                        setFormData({
                          ...formData,
                          description: e.target.value,
                        })
                      }
                      className="tw-w-full tw-px-3 tw-py-2 tw-border tw-border-gray-300 tw-rounded-lg tw-focus:tw-ring-2 tw-focus:tw-ring-blue-500 tw-focus:tw-border-transparent tw-h-24"
                      placeholder="Describe what this template is for..."
                      rows="4"
                    />
                  </div>
                </div>
              </div> */}

      {/* Available Pages */}
      {/* <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-6">
                <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900 tw-mb-4">
                  Available Pages
                </h3>

                {unusedPages.length > 0 ? (
                  <div className="tw-space-y-2 tw-max-h-64 tw-overflow-y-auto">
                    {unusedPages.map((page) => (
                      <div
                        key={page.id}
                        className="tw-flex tw-items-center tw-justify-between tw-p-3 tw-bg-gray-50 tw-rounded-lg tw-border tw-border-gray-200 tw-hover:tw-bg-gray-100 tw-transition-colors"
                      >
                        <div className="tw-flex tw-items-center">
                          <FileText className="tw-w-4 tw-h-4 tw-text-gray-400 tw-mr-2" />
                          <div>
                            <p className="tw-text-sm tw-font-medium tw-text-gray-900">
                              {page.name}
                            </p>
                            <p className="tw-text-xs tw-text-gray-500">
                              /{page.slug}
                            </p>
                          </div>
                        </div>

                        <button
                          type="button"
                          onClick={() => addPageToTemplate(page.id)}
                          className="tw-text-blue-600 tw-hover:tw-text-blue-700 tw-p-1"
                        >
                          <Plus className="tw-w-4 tw-h-4" />
                        </button>
                      </div>
                    ))}
                  </div>
                ) : (
                  <div className="tw-text-center tw-py-6">
                    <FileText className="tw-w-12 tw-h-12 tw-text-gray-300 tw-mx-auto tw-mb-3" />
                    <p className="tw-text-gray-500 tw-text-sm">
                      {formData.pages.length > 0
                        ? "All available pages have been added to this template"
                        : "No pages available. Create pages first to add them to templates."}
                    </p>
                  </div>
                )}
              </div>
            </div> */}

      {/* Right Panel - Template Pages */}
      {/* <div className="tw-space-y-6">
              <div className="tw-bg-white tw-rounded-xl tw-shadow-sm tw-border tw-border-gray-200 tw-p-6">
                <div className="tw-flex tw-items-center tw-justify-between tw-mb-4">
                  <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900">
                    Template Pages
                  </h3>
                  <span className="tw-text-sm tw-text-gray-500">
                    {formData.pages.length} pages
                  </span>
                </div>

                {formData.pages.length > 0 ? (
                  <div className="tw-space-y-2 tw-max-h-96 tw-overflow-y-auto">
                    {formData.pages.map((pageId, index) => {
                      const page = getPageById(pageId);
                      if (!page) return null;

                      return (
                        <div
                          key={pageId}
                          className="tw-flex tw-items-center tw-justify-between tw-p-3 tw-bg-blue-50 tw-rounded-lg tw-border tw-border-blue-200"
                        >
                          <div className="tw-flex tw-items-center">
                            <GripVertical className="tw-w-4 tw-h-4 tw-text-gray-400 tw-mr-2 tw-cursor-move" />
                            <FileText className="tw-w-4 tw-h-4 tw-text-blue-600 tw-mr-2" />
                            <div>
                              <p className="tw-text-sm tw-font-medium tw-text-gray-900">
                                {page.name}
                              </p>
                              <p className="tw-text-xs tw-text-gray-500">
                                /{page.slug} • Position {index + 1}
                              </p>
                            </div>
                          </div>

                          <button
                            type="button"
                            onClick={() => removePageFromTemplate(pageId)}
                            className="tw-text-red-600 tw-hover:tw-text-red-700 tw-p-1"
                          >
                            <Trash2 className="tw-w-4 tw-h-4" />
                          </button>
                        </div>
                      );
                    })}
                  </div>
                ) : (
                  <div className="tw-text-center tw-py-8 tw-border-2 tw-border-dashed tw-border-gray-300 tw-rounded-lg">
                    <Plus className="tw-w-12 tw-h-12 tw-text-gray-300 tw-mx-auto tw-mb-3" />
                    <p className="tw-text-gray-500 tw-text-sm tw-mb-2">
                      No pages in template
                    </p>
                    <p className="tw-text-gray-400 tw-text-xs">
                      Add pages from the left panel to build your template
                    </p>
                  </div>
                )}
              </div> */}

      {/* Template Summary */}
      {/* {formData.pages.length > 0 && (
                <div className="tw-bg-gradient-to-r tw-from-blue-50 tw-to-purple-50 tw-rounded-xl tw-p-6 tw-border tw-border-blue-200">
                  <h4 className="tw-text-md tw-font-semibold tw-text-gray-900 tw-mb-3">
                    Template Summary
                  </h4>
                  <div className="tw-space-y-2 tw-text-sm">
                    <div className="tw-flex tw-justify-between">
                      <span className="tw-text-gray-600">Total Pages:</span>
                      <span className="tw-font-medium">
                        {formData.pages.length}
                      </span>
                    </div>
                    <div className="tw-flex tw-justify-between">
                      <span className="tw-text-gray-600">Status:</span>
                      <span className="tw-font-medium tw-text-green-600">
                        {formData.pages.length > 0
                          ? "Ready to Save"
                          : "Add Pages"}
                      </span>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div> */}

      {/* Action Buttons */}
      {/* <div className="tw-flex tw-justify-end tw-space-x-4 tw-mt-8 tw-pt-6 tw-border-t tw-border-gray-200">
            <button
              type="button"
              onClick={onCancel}
              className="tw-px-6 tw-py-2 tw-border tw-border-gray-300 tw-text-gray-700 tw-rounded-lg tw-font-medium tw-hover:tw-bg-gray-50 tw-transition-colors"
            >
              Cancel
            </button>
            <button
              type="submit"
              disabled={saving || !formData.name}
              className="tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 tw-text-white tw-px-6 tw-py-2 tw-rounded-lg tw-font-medium tw-hover:tw-from-blue-700 tw-hover:tw-to-purple-700 tw-transition-all tw-flex tw-items-center tw-disabled:tw-opacity-50"
            >
              {saving ? (
                <>
                  <div className="tw-animate-spin tw-rounded-full tw-h-4 tw-w-4 tw-border-b-2 tw-border-white tw-mr-2"></div>
                  Saving...
                </>
              ) : (
                <>
                  <Save className="tw-w-4 tw-h-4 tw-mr-2" />
                  {template ? "Update Template" : "Create Template"}
                </>
              )}
            </button>
          </div>
        </form>
      </div> */}
    </>
  );
};

export default TemplateEditor;
