import React, { useState, useEffect, useRef, useCallback } from "react";
import { Tooltip, Collapse, Input, Spin, Select } from "antd";
import Header from "../Layout/Header";
import {
  Save,
  X,
  Eye,
  Smartphone,
  Tablet,
  Monitor,
  Settings,
  Trash2,
  GripVertical,
  ChevronLeft,
  ChevronUp,
  ChevronDown,
  Search,
  Loader2,
} from "lucide-react";
import useHttp from "../../hooks/use-http";
import { CONSTANTS } from "../../util/constant/CONSTANTS";
import { apiGenerator } from "../../util/functions";
import { DndProvider, useDrag, useDrop } from "react-dnd";
import { HTML5Backend } from "react-dnd-html5-backend";
import PageStructure from "./Component/PageStructure";
import PageSetting from "./Component/PageSetting";
import PagePreview from "./Component/PagePreview";
import { DND_TYPES } from "../../util/content";
import { generateGlobalPreviewHTML } from "../Components/content";
import PageLibrary from "./Component/PageLibrary";

const DragDropBuilder = ({ page, onSave, onCancel }) => {
  const api = useHttp();
  const [components, setComponents] = useState([]);
  const [categories, setCategories] = useState([]);
  const [pageData, setPageData] = useState({
    name: "",
    slug: "",
    meta_title: "",
    meta_description: "",
    custom_css: "",
    custom_js: "",
    components: [],
  });

  const [saving, setSaving] = useState(false);
  const [isStructureOpen, setIsStructureOpen] = useState(true);
  const [isComponentOpen, setIsComponentOpen] = useState(true);
  const [searchTerm, setSearchTerm] = useState("");
  const [debouncedSearchTerm, setDebouncedSearchTerm] = useState("");
  const [isSearching, setIsSearching] = useState(false);
  const [isMobile, setIsMobile] = useState(false);
  const [isTablet, setIsTablet] = useState(false);

  // Debouncing effect for search
  useEffect(() => {
    if (searchTerm) {
      setIsSearching(true);
      const timer = setTimeout(() => {
        setDebouncedSearchTerm(searchTerm);
        setIsSearching(false);
      }, 300); // 300ms debounce delay

      return () => {
        clearTimeout(timer);
      };
    } else {
      setDebouncedSearchTerm("");
      setIsSearching(false);
    }
  }, [searchTerm]);

  // Responsive detection
  useEffect(() => {
    const handleResize = () => {
      const width = window.innerWidth;
      setIsMobile(width < 768);
      setIsTablet(width >= 768 && width < 1024);

      // Auto-close sidebars on mobile for better UX
      if (width < 768) {
        // On mobile, close both sidebars by default
        setIsComponentOpen(false);
        setIsStructureOpen(false);
      } else if (width >= 768 && width < 1024) {
        // On tablet, allow one sidebar open
        // Keep current state but ensure at least center content is visible
      }
    };

    handleResize(); // Initial check
    window.addEventListener("resize", handleResize);
    return () => window.removeEventListener("resize", handleResize);
  }, []);

  useEffect(() => {
    // Fetch components
    api.sendRequest(CONSTANTS.API.components.get, (res) => {
      console.log("Components fetched:", res);
      setComponents(res);
    });

    // Fetch categories
    api.sendRequest(CONSTANTS.API.categories.get, (res) => {
      console.log("Categories fetched:", res);
      setCategories(res);
    });

    if (page) {
      // Ensure existing components have uniqueId for stable React keys
      const componentsWithUniqueId = (page.components || []).map(
        (comp, index) => ({
          ...comp,
          uniqueId:
            comp.uniqueId ||
            `${comp.id}-${index}-${Date.now()}-${Math.random()
              .toString(36)
              .substring(2, 11)}`,
        })
      );

      setPageData({
        name: page.name || "",
        slug: page.slug || "",
        meta_title: page.meta_title || "",
        meta_description: page.meta_description || "",
        custom_css: page.custom_css || "",
        custom_js: page.custom_js || "",
        components: componentsWithUniqueId,
      });
    }
  }, [page]);

  const handleSave = async () => {
    setSaving(true);

    const apiConfig = page
      ? apiGenerator(CONSTANTS.API.pages.update, { id: page.id })
      : CONSTANTS.API.pages.create;

    api.sendRequest(
      apiConfig,
      (res) => {
        console.log("Page saved successfully:", res);
        setSaving(false);
        onSave();
      },
      pageData,
      page ? "Page updated successfully!" : "Page created successfully!",
      (error) => {
        console.error("Error saving page:", error);
        setSaving(false);
      }
    );
  };

  // Reorder helper for immediate component moves
  const moveComponent = (fromIndex, toIndex) => {
    console.log(`Moving component from ${fromIndex} to ${toIndex}`);
    const updatedComponents = [...pageData.components];
    const [movedComponent] = updatedComponents.splice(fromIndex, 1);
    updatedComponents.splice(toIndex, 0, movedComponent);

    setPageData((prevData) => ({
      ...prevData,
      components: updatedComponents,
    }));
  };

  const removeComponentFromPage = (index) => {
    console.log("Removing component at index:", index);
    const updatedComponents = pageData.components.filter((_, i) => i !== index);

    setPageData((prevData) => ({
      ...prevData,
      components: updatedComponents,
    }));
  };

  // Update CSS class for a specific component in page structure
  const handleCssChange = (index, value) => {
    const updated = [...pageData.components];
    updated[index] = { ...updated[index], cssClass: value };

    setPageData((prevData) => ({
      ...prevData,
      components: updated,
    }));
  };

  // Global onChange handler for all component fields
  const handleComponentFieldChange = (index, field, value) => {
    const updated = [...pageData.components];
    updated[index] = { ...updated[index], [field]: value };

    setPageData((prevData) => ({
      ...prevData,
      components: updated,
    }));
  };

  const groupedComponents = categories.reduce((acc, category) => {
    const categoryComponents = components.filter(
      (comp) => comp.category_id === category.id
    );
    const filteredComponents = debouncedSearchTerm
      ? categoryComponents.filter((comp) =>
          comp.name.toLowerCase().includes(debouncedSearchTerm.toLowerCase())
        )
      : categoryComponents;

    if (filteredComponents.length > 0) {
      acc[category.id] = {
        name: category.name,
        color: category.color,
        components: filteredComponents,
      };
    }
    return acc;
  }, {});

  const handleGlobalFieldChange = (index, field, value) => {
    // if (onComponentFieldChange) {
    //   onComponentFieldChange(index, field, value);
    // }
  };

  return (
    <DndProvider backend={HTML5Backend}>
      <div className="tw-h-screen tw-flex tw-overflow-hidden tw-relative">
        {/* Mobile Backdrop */}
        {isMobile && (isComponentOpen || isStructureOpen) && (
          <div
            className="tw-fixed tw-inset-0 tw-bg-black tw-bg-opacity-50 tw-z-40"
            onClick={() => {
              setIsComponentOpen(false);
              setIsStructureOpen(false);
            }}
          />
        )}

        {/* Left Sidebar - Components Library */}
        <PageLibrary
          isComponentOpen={isComponentOpen}
          setIsComponentOpen={setIsComponentOpen}
          isMobile={isMobile}
          isTablet={isTablet}
          type={DND_TYPES.LIB_ITEM}
          listData={groupedComponents}
          isLoading={isSearching}
          onSearchChange={(e) => setSearchTerm(e)}
          handleGlobalFieldChange={handleGlobalFieldChange}
          // LibraryItem={LibraryItem}
        />

        <div className="tw-flex-1 tw-w-full tw-flex tw-flex-col">
          <PagePreview
            pageData={pageData}
            setPageData={setPageData}
            components={components}
            handleSave={handleSave}
            saving={saving}
            onCancel={onCancel}
            isStructureOpen={isStructureOpen}
            setIsStructureOpen={setIsStructureOpen}
            isComponentOpen={isComponentOpen}
            setIsComponentOpen={setIsComponentOpen}
          />
        </div>

        <PageStructure
          isStructureOpen={isStructureOpen}
          setIsStructureOpen={setIsStructureOpen}
          pageData={pageData}
          setPageData={setPageData}
          components={components}
          handleCssChange={handleCssChange}
          removeComponentFromPage={removeComponentFromPage}
          moveComponent={moveComponent}
          onComponentFieldChange={handleComponentFieldChange}
        />
      </div>
    </DndProvider>
  );
};

export default DragDropBuilder;
