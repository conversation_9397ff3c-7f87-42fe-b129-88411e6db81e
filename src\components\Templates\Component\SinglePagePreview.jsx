import React, { useEffect, useRef, useState } from "react";
import { useDragLayer } from "react-dnd";

const SinglePagePreview = ({ templatePage, originalPage, previewMode }) => {
  const containerRef = useRef(null);
  const [scale, setScale] = useState(1);
  const [isDragging, setIsDragging] = useState(false);
  // Get current device dimensions
  const { width: deviceWidth, height: deviceHeight } =
    DEVICE_SIZES[previewMode];
  // Global drag detection
  const { isDraggingGlobal } = useDragLayer((monitor) => ({
    isDraggingGlobal: monitor.isDragging(),
  }));

  // Update local dragging state based on global drag state
  useEffect(() => {
    if (isDraggingGlobal) {
      setIsDragging(true);
    } else {
      // Small delay to allow drop to complete
      const timer = setTimeout(() => setIsDragging(false), 100);
      return () => clearTimeout(timer);
    }
  }, [isDraggingGlobal]);

  // Scale calculation function (similar to PagePreview)
  const recalcScale = () => {
    if (!containerRef.current) return;
    const bounds = containerRef.current.getBoundingClientRect();
    // Add padding to ensure device doesn't fill entire container
    const availableWidth = bounds.width - (previewMode == "laptop" ? 30 : 60); // 150px padding on each side
    const availableHeight = bounds.height - (previewMode == "laptop" ? 30 : 60); // 150px padding on top/bottom
    const widthScale = availableWidth / deviceWidth;
    const heightScale = availableHeight / deviceHeight;
    requestAnimationFrame(() => {
      setScale(Math.min(widthScale, heightScale, 1)); // Don't scale up beyond 100%
    });
  };

  // Update scale on mount & when device changes
  useEffect(() => {
    recalcScale();
    const resizeObserver = new ResizeObserver(recalcScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    return () => resizeObserver.disconnect();
  }, [previewMode, deviceWidth, deviceHeight]);

  // Template Page Preview Component - Single device frame like PagePreview
  const TemplatePagePreview = ({ templatePage, originalPage }) => {
    console.log(templatePage, originalPage);
    const generatePagePreviewHTML = () => {
      if (!originalPage) return "";
      return generateGlobalPreviewHTML({
        type: "page",
        data: originalPage.components || [],
        pageData: originalPage,
        customCSS: originalPage.custom_css,
        customJS: originalPage.custom_js,
        title: templatePage.name || originalPage.name,
      });
    };

    return (
      <div className="tw-bg-gray-100 tw-rounded-xl tw-flex tw-justify-center tw-items-center tw-relative tw-min-h-[400px]">
        {/* Virtual device frame - exactly like PagePreview */}
        <div
          className="device-frame tw-bg-white tw-rounded-xl tw-shadow-xl tw-border tw-border-gray-200 tw-absolute"
          style={{
            width: `${deviceWidth}px`,
            height: `${deviceHeight}px`,
            transform: `scale(${scale})`,
            left: "50%",
            top: "50%",
            marginLeft: `-${deviceWidth / 2}px`,
            marginTop: `-${deviceHeight / 2}px`,
            // transition: "all 0.3s ease",
          }}
        >
          <div className="tw-relative tw-w-full tw-h-full tw-flex tw-flex-col tw-overflow-hidden tw-rounded-xl">
            {originalPage ? (
              <iframe
                srcDoc={generatePagePreviewHTML()}
                className="tw-w-full tw-h-full tw-border-0 tw-rounded-xl"
                title={`${templatePage.name} Preview`}
                style={{
                  pointerEvents: "none",
                  background: "#fff",
                }}
              />
            ) : (
              <div className="tw-flex tw-items-center tw-justify-center tw-h-full ">
                <div className="tw-text-center">
                  <FileText className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                  <p className="tw-text-gray-500 tw-mb-2">Page not found</p>
                  <p className="tw-text-sm tw-text-gray-400">
                    This page may have been deleted
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );

    return (
      <div className="tw-bg-gray-100 tw-rounded-xl tw-flex tw-justify-center tw-items-center tw-relative tw-min-h-[400px]">
        {/* Virtual device frame - exactly like PagePreview */}
        <div
          className="device-frame tw-bg-white tw-rounded-xl tw-shadow-xl tw-border tw-border-gray-200 tw-absolute"
          style={{
            width: `${deviceWidth}px`,
            height: `${deviceHeight}px`,
            transform: `scale(${scale})`,
            left: "50%",
            top: "50%",
            marginLeft: `-${deviceWidth / 2}px`,
            marginTop: `-${deviceHeight / 2}px`,
            // transition: "all 0.3s ease",
          }}
        >
          <div className="tw-relative tw-w-full tw-h-full tw-flex tw-flex-col tw-overflow-hidden tw-rounded-xl">
            {originalPage ? (
              <iframe
                srcDoc={generatePagePreviewHTML()}
                className="tw-w-full tw-h-full tw-border-0 tw-rounded-xl"
                title={`${templatePage.name} Preview`}
                style={{
                  pointerEvents: "none",
                  background: "#fff",
                }}
              />
            ) : (
              <div className="tw-flex tw-items-center tw-justify-center tw-h-full ">
                <div className="tw-text-center">
                  <FileText className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                  <p className="tw-text-gray-500 tw-mb-2">Page not found</p>
                  <p className="tw-text-sm tw-text-gray-400">
                    This page may have been deleted
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  return (
    <div
      ref={containerRef}
      className="tw-w-full tw-h-[34rem] tw-bg-gray-100 tw-overflow-auto tw-relative tw-rounded-lg tw-p-4"
    >
      <TemplatePagePreview
        key={templatePage.id}
        templatePage={templatePage}
        originalPage={originalPage}
      />
    </div>
  );
};

export default SinglePagePreview;
