import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { ChevronLeft, Eye, FileText, Plus } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import {
  deviceConfigs,
  generateGlobalPreviewHTML,
} from "../../Components/content";
import { useDragLayer, useDrop } from "react-dnd";

// Device sizes for responsive preview (similar to demo)
const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

const TemplatePreview = ({
  isPageLibraryOpen,
  setIsPageLibraryOpen,
  isTemplateStructureOpen,
  setIsTemplateStructureOpen,
  formData,
  setFormData,
  pages,
  handleSave,
  saving,
  onCancel,
}) => {
  const [previewMode, setPreviewMode] = useState("laptop");
  const [isDragging, setIsDragging] = useState(false);
  const deviceConfig = deviceConfigs(previewMode);
  const [scale, setScale] = useState(1);
  const containerRef = useRef(null);

  // Get current device dimensions
  const { width: deviceWidth, height: deviceHeight } =
    DEVICE_SIZES[previewMode];
  // Global drag detection
  const { isDraggingGlobal } = useDragLayer((monitor) => ({
    isDraggingGlobal: monitor.isDragging(),
  }));

  // Update local dragging state based on global drag state
  useEffect(() => {
    if (isDraggingGlobal) {
      setIsDragging(true);
    } else {
      // Small delay to allow drop to complete
      const timer = setTimeout(() => setIsDragging(false), 100);
      return () => clearTimeout(timer);
    }
  }, [isDraggingGlobal]);

  // Add page to template
  const addPageToTemplate = (page) => {
    const templatePage = {
      id: page.id,
      name: page.name,
      slug: page.slug,
      version: "v1",
      url: `/${page.slug}`,
      type: "static",
      showNavbar: true,
      navPosition: formData.pages?.length || 0,
      order: formData.pages?.length || 0,
    };

    const updatedPages = [...(formData.pages || []), templatePage];
    setFormData({ ...formData, pages: updatedPages });
  };

  // Template Page Preview Component with responsive sizing
  const TemplatePagePreview = ({ templatePage, originalPage }) => {
    const generatePagePreviewHTML = () => {
      if (!originalPage) return "";
      return generateGlobalPreviewHTML({
        type: "page",
        data: originalPage.components || [],
        pageData: originalPage,
        customCSS: originalPage.custom_css,
        customJS: originalPage.custom_js,
        title: templatePage.name || originalPage.name,
      });
    };

    // Calculate responsive dimensions based on current preview mode
    const getPreviewDimensions = () => {
      const { width: deviceWidth, height: deviceHeight } =
        DEVICE_SIZES[previewMode];

      // Base container dimensions for different device types
      const containerDimensions = {
        mobile: { width: 200, height: 320 },
        tablet: { width: 280, height: 360 },
        laptop: { width: 360, height: 240 },
      };

      const container = containerDimensions[previewMode];

      // Calculate scale to fit device content in container
      const scaleX = container.width / deviceWidth;
      const scaleY = container.height / deviceHeight;
      const scale = Math.min(scaleX, scaleY);

      return {
        containerWidth: container.width,
        containerHeight: container.height,
        scale,
        deviceWidth,
        deviceHeight,
      };
    };

    const dimensions = getPreviewDimensions();

    return (
      <div className="tw-bg-white tw-rounded-lg tw-border tw-border-gray-200 tw-shadow-sm tw-overflow-hidden tw-mb-4">
        {/* Page Info Header */}
        <div className="tw-p-3 tw-border-b tw-border-gray-100">
          <div className="tw-flex tw-items-center tw-justify-between">
            <div>
              <h4 className="tw-text-sm tw-font-medium tw-text-gray-900 tw-mb-1">
                {templatePage.name}
              </h4>
              <p className="tw-text-xs tw-text-gray-500">
                {templatePage.url} • {previewMode} view
              </p>
            </div>
            <div className="tw-text-xs tw-text-gray-400">
              {dimensions.deviceWidth} × {dimensions.deviceHeight}
            </div>
          </div>
        </div>

        {/* Responsive Preview Container */}
        <div
          className="tw-bg-gray-50 tw-flex tw-items-center tw-justify-center tw-p-4"
          style={{
            height: dimensions.containerHeight + 32, // Add padding
            minHeight: dimensions.containerHeight + 32,
          }}
        >
          {originalPage ? (
            <div
              className="tw-bg-white tw-rounded tw-shadow-sm tw-overflow-hidden tw-border tw-border-gray-200"
              style={{
                width: dimensions.containerWidth,
                height: dimensions.containerHeight,
                position: "relative",
              }}
            >
              <iframe
                srcDoc={generatePagePreviewHTML()}
                className="tw-border-0 tw-origin-top-left"
                title={`${templatePage.name} Preview`}
                style={{
                  width: dimensions.deviceWidth,
                  height: dimensions.deviceHeight,
                  transform: `scale(${dimensions.scale})`,
                  pointerEvents: "none",
                  transformOrigin: "top left",
                }}
              />
            </div>
          ) : (
            <div
              className="tw-bg-white tw-rounded tw-shadow-sm tw-border tw-border-gray-200 tw-flex tw-items-center tw-justify-center"
              style={{
                width: dimensions.containerWidth,
                height: dimensions.containerHeight,
              }}
            >
              <div className="tw-text-center tw-text-gray-400">
                <FileText className="tw-w-8 tw-h-8 tw-mx-auto tw-mb-2" />
                <p className="tw-text-xs">Page not found</p>
              </div>
            </div>
          )}
        </div>

        {/* Page Details Footer */}
        <div className="tw-p-3 tw-bg-gray-50 tw-border-t tw-border-gray-100">
          <div className="tw-flex tw-items-center tw-justify-between tw-text-xs tw-text-gray-500">
            <div className="tw-flex tw-items-center tw-space-x-4">
              <span>Type: {templatePage.type}</span>
              <span>Navbar: {templatePage.showNavbar ? "Yes" : "No"}</span>
            </div>
            <span>Position: {templatePage.navPosition}</span>
          </div>
        </div>
      </div>
    );
  };

  // Drop zone for template preview area
  const TemplateDropZone = () => {
    const [{ isOver, canDrop }, drop] = useDrop(
      () => ({
        accept: "PAGE_ITEM",
        drop: (item, monitor) => {
          if (monitor.didDrop()) return;
          console.log("Dropping page:", item.page);
          addPageToTemplate(item.page);
        },
        collect: (monitor) => ({
          isOver: monitor.isOver({ shallow: true }),
          canDrop: monitor.canDrop(),
        }),
      }),
      [formData.pages]
    );

    const showDropIndicator = isOver && canDrop;

    // Scale calculation function (similar to demo)
    const recalcScale = () => {
      if (!containerRef.current) return;
      const bounds = containerRef.current.getBoundingClientRect();
      // Add padding to ensure device doesn't fill entire container
      const availableWidth = bounds.width - 30; // 20px padding on each side
      const availableHeight = bounds.height - 30; // 20px padding on top/bottom
      const widthScale = availableWidth / deviceWidth;
      const heightScale = availableHeight / deviceHeight;
      setScale(Math.min(widthScale, heightScale, 1)); // Don't scale up beyond 100%
    };

    // Update scale on mount & when device changes
    useEffect(() => {
      recalcScale();
      const resizeObserver = new ResizeObserver(recalcScale);
      if (containerRef.current) {
        resizeObserver.observe(containerRef.current);
      }
      return () => resizeObserver.disconnect();
    }, [previewMode, deviceWidth, deviceHeight]);

    return (
      <div
        ref={drop}
        className={`tw-absolute tw-inset-0 tw-transition-all tw-duration-150 ${
          showDropIndicator ? "tw-z-30" : "tw-z-10"
        } ${
          showDropIndicator
            ? "tw-bg-blue-50/60 tw-border-2 tw-border-dashed tw-border-blue-400"
            : ""
        }`}
        style={{ pointerEvents: "auto" }}
      >
        {showDropIndicator && (
          <div className="tw-flex tw-items-center tw-justify-center tw-h-full tw-pointer-events-none">
            <div className="tw-bg-white tw-rounded-lg tw-p-6 tw-shadow-xl tw-border-2 tw-border-blue-300 tw-animate-pulse">
              <div className="tw-flex tw-items-center tw-space-x-3">
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce"></div>
                <p className="tw-text-blue-700 tw-font-semibold tw-text-lg">
                  Drop to add page to template
                </p>
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <div className="tw-flex-1 tw-w-full tw-flex tw-flex-col tw-bg-gray-50">
        {/* Top Toolbar */}
        <div className="tw-flex tw-w-full tw-bg-white tw-border-b tw-border-gray-200 tw-space-x-4">
          {!isPageLibraryOpen && (
            <div className="tw-flex tw-items-center tw-justify-center tw-border-r tw-border-gray-200">
              <Tooltip title="Show Page Library">
                <button
                  onClick={() => setIsPageLibraryOpen(true)}
                  className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                >
                  <ChevronLeft size={30} className="tw-rotate-180" />
                </button>
              </Tooltip>
            </div>
          )}
          <div className="tw-p-3 md:tw-p-[21px] tw-flex tw-w-full tw-items-center tw-justify-between">
            <div className="tw-flex tw-items-center tw-space-x-4">
              <div className="tw-rounded-lg tw-hidden md:tw-flex">
                <div className="tw-flex tw-items-center tw-space-x-1  tw-rounded-lg  tw-mb-1">
                  {Object.entries(deviceConfig)?.map(([key, config]) => (
                    <Tooltip
                      key={key}
                      title={`${config.label} (${config.description})`}
                    >
                      <button
                        type="button"
                        onClick={() => setPreviewMode(key)}
                        className={`tw-flex tw-items-center tw-justify-center tw-w-8 tw-h-8 tw-rounded-md tw-transition-all tw-duration-200 ${
                          previewMode === key
                            ? "tw-bg-[#EAF0FD] tw-text-white tw-shadow-sm"
                            : "tw-text-gray-500 tw-hover:tw-bg-gray-100 tw-hover:tw-text-gray-700"
                        }`}
                      >
                        {config.icon}
                      </button>
                    </Tooltip>
                  ))}
                </div>
              </div>
            </div>
            <div className="tw-flex tw-items-center tw-space-x-2">
              <button
                onClick={onCancel}
                className="tw-px-4 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors"
              >
                Cancel
              </button>

              <Button
                type="primary"
                size="large"
                onClick={handleSave}
                disabled={saving}
                // || !pageData.name || !pageData.slug}
                className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
              >
                {saving ? (
                  <>
                    <div className="tw-animate-spin tw-rounded-full tw-h-4 tw-w-4 tw-border-b-2 tw-border-white tw-mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>Save</>
                )}
              </Button>
            </div>
          </div>
          {!isTemplateStructureOpen && (
            <div className="tw-flex tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
              <Tooltip title="Show Template Structure">
                <button
                  onClick={() => setIsTemplateStructureOpen(true)}
                  className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                >
                  <ChevronLeft size={30} className="tw-rotate-180" />
                </button>
              </Tooltip>
            </div>
          )}
        </div>

        {/* Preview Content */}
        <div className="tw-flex-1 tw-overflow-auto tw-p-4 tw-relative">
          {formData.pages?.length > 0 ? (
            <div className="tw-max-w-full">
              {/* Template Summary */}
              <div className="tw-mb-6 tw-bg-white tw-rounded-lg tw-border tw-border-gray-200 tw-p-4">
                <div className="tw-flex tw-items-center tw-justify-between tw-mb-2">
                  <h3 className="tw-text-lg tw-font-semibold tw-text-gray-900">
                    Template Preview
                  </h3>
                  <div className="tw-flex tw-items-center tw-space-x-2">
                    <span className="tw-text-sm tw-text-gray-500">
                      {formData.pages.length} page
                      {formData.pages.length !== 1 ? "s" : ""}
                    </span>
                    <span className="tw-text-xs tw-bg-blue-100 tw-text-blue-800 tw-px-2 tw-py-1 tw-rounded-full">
                      {previewMode} view
                    </span>
                  </div>
                </div>
                <p className="tw-text-sm tw-text-gray-600">
                  Preview how your template pages will look across different
                  devices. Use the device buttons above to switch between
                  mobile, tablet, and desktop views.
                </p>
              </div>

              {/* Responsive Page Grid */}
              <div
                className={`tw-grid tw-gap-6 ${
                  previewMode === "mobile"
                    ? "tw-grid-cols-1 sm:tw-grid-cols-2 lg:tw-grid-cols-3 xl:tw-grid-cols-4"
                    : previewMode === "tablet"
                    ? "tw-grid-cols-1 lg:tw-grid-cols-2 xl:tw-grid-cols-3"
                    : "tw-grid-cols-1 xl:tw-grid-cols-2"
                }`}
              >
                {formData.pages.map((templatePage) => {
                  const originalPage = pages.find(
                    (p) => p.id === templatePage.id
                  );
                  return (
                    <TemplatePagePreview
                      key={templatePage.id}
                      templatePage={templatePage}
                      originalPage={originalPage}
                    />
                  );
                })}
              </div>
            </div>
          ) : (
            <div className="tw-flex tw-items-center tw-justify-center tw-h-full">
              <div className="tw-text-center tw-max-w-md tw-mx-auto">
                <div className="tw-bg-white tw-rounded-lg tw-border tw-border-gray-200 tw-p-8 tw-shadow-sm">
                  <Plus className="tw-w-16 tw-h-16 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                  <h3 className="tw-text-lg tw-font-medium tw-text-gray-900 tw-mb-2">
                    Start Building Your Template
                  </h3>
                  <p className="tw-text-gray-500 tw-mb-4">
                    No pages in template yet. Drag pages from the left panel to
                    build your template.
                  </p>
                  <div className="tw-bg-blue-50 tw-rounded-lg tw-p-4">
                    <p className="tw-text-sm tw-text-blue-700">
                      💡 <strong>Tip:</strong> You can preview how your template
                      will look on different devices using the device buttons in
                      the toolbar above.
                    </p>
                  </div>
                </div>
              </div>
            </div>
          )}
          <TemplateDropZone />
        </div>
      </div>
    </>
  );
};

export default TemplatePreview;
