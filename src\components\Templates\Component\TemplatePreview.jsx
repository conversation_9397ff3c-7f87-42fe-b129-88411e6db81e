import { <PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "antd";
import { ChevronLeft, Eye, FileText, Plus } from "lucide-react";
import React, { useEffect, useRef, useState } from "react";
import {
  deviceConfigs,
  generateGlobalPreviewHTML,
} from "../../Components/content";
import { useDragLayer, useDrop } from "react-dnd";

// Device sizes for responsive preview (similar to demo)
const DEVICE_SIZES = {
  mobile: { width: 375, height: 667 },
  tablet: { width: 768, height: 1024 },
  laptop: { width: 1200, height: 800 },
};

const TemplatePreview = ({
  isPageLibraryOpen,
  setIsPageLibraryOpen,
  isTemplateStructureOpen,
  setIsTemplateStructureOpen,
  formData,
  setFormData,
  pages,
  handleSave,
  saving,
  onCancel,
}) => {
  const [previewMode, setPreviewMode] = useState("laptop");
  const [isDragging, setIsDragging] = useState(false);
  const deviceConfig = deviceConfigs(previewMode);
  const [scale, setScale] = useState(1);
  const containerRef = useRef(null);

  // Get current device dimensions
  const { width: deviceWidth, height: deviceHeight } =
    DEVICE_SIZES[previewMode];
  // Global drag detection
  const { isDraggingGlobal } = useDragLayer((monitor) => ({
    isDraggingGlobal: monitor.isDragging(),
  }));

  // Scale calculation function (similar to PagePreview)
  const recalcScale = () => {
    if (!containerRef.current) return;
    const bounds = containerRef.current.getBoundingClientRect();
    // Add padding to ensure device doesn't fill entire container
    const availableWidth = bounds.width - 30; // 30px padding on each side
    const availableHeight = bounds.height - 30; // 30px padding on top/bottom
    const widthScale = availableWidth / deviceWidth;
    const heightScale = availableHeight / deviceHeight;
    setScale(Math.min(widthScale, heightScale, 1)); // Don't scale up beyond 100%
  };

  // Update scale on mount & when device changes
  useEffect(() => {
    recalcScale();
    const resizeObserver = new ResizeObserver(recalcScale);
    if (containerRef.current) {
      resizeObserver.observe(containerRef.current);
    }
    return () => resizeObserver.disconnect();
  }, [previewMode, deviceWidth, deviceHeight]);

  // Update local dragging state based on global drag state
  useEffect(() => {
    if (isDraggingGlobal) {
      setIsDragging(true);
    } else {
      // Small delay to allow drop to complete
      const timer = setTimeout(() => setIsDragging(false), 100);
      return () => clearTimeout(timer);
    }
  }, [isDraggingGlobal]);

  // Add page to template
  const addPageToTemplate = (page) => {
    const templatePage = {
      id: page.id,
      name: page.name,
      slug: page.slug,
      version: "v1",
      url: `/${page.slug}`,
      type: "static",
      showNavbar: true,
      navPosition: formData.pages?.length || 0,
      order: formData.pages?.length || 0,
    };

    const updatedPages = [...(formData.pages || []), templatePage];
    setFormData({ ...formData, pages: updatedPages });
  };

  // Template Page Preview Component - Single device frame like PagePreview
  const TemplatePagePreview = ({ templatePage, originalPage }) => {
    const generatePagePreviewHTML = () => {
      if (!originalPage) return "";
      return generateGlobalPreviewHTML({
        type: "page",
        data: originalPage.components || [],
        pageData: originalPage,
        customCSS: originalPage.custom_css,
        customJS: originalPage.custom_js,
        title: templatePage.name || originalPage.name,
      });
    };

    return (
      <div className="tw-bg-gray-100 tw-rounded-xl tw-flex tw-justify-center tw-items-center tw-relative ">
        {/* Virtual device frame - exactly like PagePreview */}
        <div
          className="tw-bg-white tw-rounded-xl tw-shadow-xl tw-border tw-border-gray-200 tw-absolute"
          style={{
            width: `${deviceWidth}px`,
            height: `${deviceHeight}px`,
            transform: `scale(${scale})`,
            left: "50%",
            top: "75%",
            marginLeft: `-${deviceWidth / 2}px`,
            marginTop: `-${deviceHeight / 2}px`,
            transition: "transform 0.3s ease",
          }}
        >
          <div className="tw-relative tw-w-full tw-h-full tw-flex tw-flex-col tw-overflow-hidden tw-rounded-xl">
            {originalPage ? (
              <iframe
                srcDoc={generatePagePreviewHTML()}
                className="tw-w-full tw-h-full tw-border-0 tw-rounded-xl"
                title={`${templatePage.name} Preview`}
                style={{
                  pointerEvents: "none",
                  background: "#fff",
                }}
              />
            ) : (
              <div className="tw-flex tw-items-center tw-justify-center tw-h-full ">
                <div className="tw-text-center">
                  <FileText className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                  <p className="tw-text-gray-500 tw-mb-2">Page not found</p>
                  <p className="tw-text-sm tw-text-gray-400">
                    This page may have been deleted
                  </p>
                </div>
              </div>
            )}
          </div>
        </div>
      </div>
    );
  };

  // Drop zone for template preview area
  const TemplateDropZone = () => {
    const [{ isOver, canDrop }, drop] = useDrop(
      () => ({
        accept: "PAGE_ITEM",
        drop: (item, monitor) => {
          if (monitor.didDrop()) return;
          console.log("Dropping page:", item.page);
          addPageToTemplate(item.page);
        },
        collect: (monitor) => ({
          isOver: monitor.isOver({ shallow: true }),
          canDrop: monitor.canDrop(),
        }),
      }),
      [formData.pages]
    );

    const showDropIndicator = isOver && canDrop;

    return (
      <div
        ref={drop}
        className={`tw-absolute tw-inset-0 tw-transition-all tw-duration-150 ${
          showDropIndicator ? "tw-z-30" : "tw-z-10"
        } ${
          showDropIndicator
            ? "tw-bg-blue-50/60 tw-border-2 tw-border-dashed tw-border-blue-400"
            : ""
        }`}
        style={{
          minHeight: "400px",
          pointerEvents: "auto", // Always allow pointer events for drop zone
        }}
      >
        {showDropIndicator && (
          <div className="tw-flex tw-items-center tw-justify-center tw-h-full tw-pointer-events-none">
            <div className="tw-bg-white tw-rounded-lg tw-p-6 tw-shadow-xl tw-border-2 tw-border-blue-300 tw-animate-pulse">
              <div className="tw-flex tw-items-center tw-space-x-3">
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce"></div>
                <p className="tw-text-blue-700 tw-font-semibold tw-text-lg">
                  Drop to add page to template
                </p>
                <div className="tw-w-3 tw-h-3 tw-bg-blue-500 tw-rounded-full tw-animate-bounce"></div>
              </div>
            </div>
          </div>
        )}
      </div>
    );
  };

  return (
    <>
      <div className="tw-flex-1 tw-w-full tw-flex tw-flex-col">
        {/* Top Toolbar */}
        <div className="tw-flex tw-w-full tw-bg-white tw-border-b tw-border-gray-200 tw-space-x-4">
          {!isPageLibraryOpen && (
            <div className="tw-flex tw-items-center tw-justify-center tw-border-r tw-border-gray-200">
              <Tooltip title="Show Page Library">
                <button
                  onClick={() => setIsPageLibraryOpen(true)}
                  className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                >
                  <ChevronLeft size={30} className="tw-rotate-180" />
                </button>
              </Tooltip>
            </div>
          )}
          <div className="tw-p-3 md:tw-p-[21px] tw-flex tw-w-full tw-items-center tw-justify-between">
            <div className="tw-flex tw-items-center tw-space-x-4">
              <div className="tw-rounded-lg tw-hidden md:tw-flex">
                <div className="tw-flex tw-items-center tw-space-x-1  tw-rounded-lg  tw-mb-1">
                  {Object.entries(deviceConfig)?.map(([key, config]) => (
                    <Tooltip
                      key={key}
                      title={`${config.label} (${config.description})`}
                    >
                      <button
                        type="button"
                        onClick={() => setPreviewMode(key)}
                        className={`tw-flex tw-items-center tw-justify-center tw-w-8 tw-h-8 tw-rounded-md tw-transition-all tw-duration-200 ${
                          previewMode === key
                            ? "tw-bg-[#EAF0FD] tw-text-white tw-shadow-sm"
                            : "tw-text-gray-500 tw-hover:tw-bg-gray-100 tw-hover:tw-text-gray-700"
                        }`}
                      >
                        {config.icon}
                      </button>
                    </Tooltip>
                  ))}
                </div>
              </div>
            </div>
            <div className="tw-flex tw-items-center tw-space-x-2">
              <button
                onClick={onCancel}
                className="tw-px-4 tw-py-2 tw-text-gray-600 tw-hover:tw-bg-gray-100 tw-rounded-lg tw-transition-colors"
              >
                Cancel
              </button>

              <Button
                type="primary"
                size="large"
                onClick={handleSave}
                disabled={saving}
                // || !pageData.name || !pageData.slug}
                className="tw-px-6 tw-h-10 tw-rounded-lg tw-font-medium tw-bg-gradient-to-r tw-from-blue-600 tw-to-purple-600 hover:tw-from-blue-700 hover:tw-to-purple-700 tw-border-0"
              >
                {saving ? (
                  <>
                    <div className="tw-animate-spin tw-rounded-full tw-h-4 tw-w-4 tw-border-b-2 tw-border-white tw-mr-2"></div>
                    Saving...
                  </>
                ) : (
                  <>Save</>
                )}
              </Button>
            </div>
          </div>
          {!isTemplateStructureOpen && (
            <div className="tw-flex tw-items-center tw-justify-center tw-border-l tw-border-gray-200">
              <Tooltip title="Show Template Structure">
                <button
                  onClick={() => setIsTemplateStructureOpen(true)}
                  className="tw-text-gray-600 tw-px-3 tw-flex tw-items-center tw-justify-center tw-rounded-lg"
                >
                  <ChevronLeft size={30} className="tw-rotate-180" />
                </button>
              </Tooltip>
            </div>
          )}
        </div>

        {/* Preview Content */}
        <div className="tw-p-1 md:tw-p-4 tw-w-full tw-h-full tw-bg-gray-50">
          <div
            ref={containerRef}
            className="tw-w-full tw-h-[34rem] tw-bg-gray-100 tw-flex tw-justify-center tw-items-center tw-overflow-hidden tw-relative tw-rounded-lg"
            //   className="tw-flex-1 tw-overflow-auto tw-p-4 tw-relative tw-bg-gray-50"
          >
            {formData.pages?.length > 0 ? (
              //   <div className="">
              formData.pages.map((templatePage) => {
                const originalPage = pages.find(
                  (p) => p.id === templatePage.id
                );
                return (
                  <TemplatePagePreview
                    key={templatePage.id}
                    templatePage={templatePage}
                    originalPage={originalPage}
                  />
                );
              })
            ) : (
              //   </div>
              <div className="tw-flex tw-items-center tw-justify-center tw-h-full">
                <div className="tw-text-center">
                  <Plus className="tw-w-12 tw-h-12 tw-text-gray-400 tw-mx-auto tw-mb-4" />
                  <p className="tw-text-gray-500 tw-mb-2">
                    No pages in template yet
                  </p>
                  <p className="tw-text-sm tw-text-gray-400">
                    Drag pages from the left panel to build your template
                  </p>
                </div>
              </div>
            )}
            <TemplateDropZone />
          </div>
        </div>
      </div>
    </>
  );
};

export default TemplatePreview;
